//
//  OnetoolsManager.swift
//  AppSigner
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/18.
//

import Foundation

class OnetoolsManager {
    static let shared = OnetoolsManager()
    
    private init() {}
    
    /// 获取 onetools 工具的路径
    /// - Returns: onetools 工具的完整路径
    func getOnetoolsPath() -> String {
        // 1. 直接使用Bundle查找onetools资源文件（推荐方式）
        if let onetoolsPath = Bundle.main.path(forResource: "onetools", ofType: nil) {
            return onetoolsPath
        }
        
        // 2. 如果onetools在子目录中，尝试在onetools文件夹中查找
        if let onetoolsPath = Bundle.main.path(forResource: "onetools", ofType: nil, inDirectory: "onetools") {
            return onetoolsPath
        }
        
        // 3. 如果都找不到，返回Bundle资源路径下的默认位置
        return (Bundle.main.resourcePath ?? "") + "/onetools"
    }
    
    /// 验证并准备 onetools 工具
    /// - Parameter logHandler: 日志处理回调，可选
    /// - Returns: 验证成功返回 onetools 路径，失败返回 nil
    func validateAndPrepareOnetools(logHandler: ((String) -> Void)? = nil) -> String? {
        let onetoolsPath = getOnetoolsPath()
        logHandler?("onetools路径: \(onetoolsPath)")
        
        // 检查onetools文件是否存在
        guard FileManager.default.fileExists(atPath: onetoolsPath) else {
            logHandler?("错误: 找不到onetools工具，路径: \(onetoolsPath)")
            return nil
        }
        
        // 检查文件是否可执行
        if !FileManager.default.isExecutableFile(atPath: onetoolsPath) {
            logHandler?("错误: onetools文件没有执行权限，路径: \(onetoolsPath)")
            logHandler?("尝试添加执行权限...")
            
            // 尝试添加执行权限
            let chmodProcess = Process()
            chmodProcess.launchPath = "/bin/chmod"
            chmodProcess.arguments = ["+x", onetoolsPath]
            chmodProcess.launch()
            chmodProcess.waitUntilExit()
            
            if chmodProcess.terminationStatus == 0 {
                logHandler?("成功添加执行权限")
            } else {
                logHandler?("添加执行权限失败")
                return nil
            }
        }
        
        return onetoolsPath
    }
    
    /// 执行 onetools 命令
    /// - Parameters:
    ///   - arguments: 命令参数
    ///   - logHandler: 日志处理回调，可选
    /// - Returns: 命令执行结果
    func executeOnetools(arguments: [String], logHandler: ((String) -> Void)? = nil) -> AppSignerTaskOutput? {
        guard let onetoolsPath = validateAndPrepareOnetools(logHandler: logHandler) else {
            return nil
        }
        
        logHandler?("执行命令: \(onetoolsPath) \(arguments.joined(separator: " "))")
        
        let taskOutput = Process().execute(onetoolsPath, workingDirectory: nil, arguments: arguments)
        
        if taskOutput.status != 0 {
            logHandler?("onetools 命令执行失败，状态码: \(taskOutput.status)")
            logHandler?("错误输出: \(taskOutput.output)")
        }
        
        return taskOutput
    }
}
