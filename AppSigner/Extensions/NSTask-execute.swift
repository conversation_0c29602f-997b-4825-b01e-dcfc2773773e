//
//  NSTask-execute.swift
//  AppSigner
//
//  Created by <PERSON> on 11/3/15.
//  Copyright © 2015 <PERSON>. All rights reserved.
//

import Foundation
struct AppSignerTaskOutput {
    var output: String
    var status: Int32
    init(status: Int32, output: String){
        self.status = status
        self.output = output
    }
}
extension Process {
    func launchSynchronous() -> AppSignerTaskOutput {
        self.standardInput = FileHandle.nullDevice
        let pipe = Pipe()
        self.standardOutput = pipe
        self.standardError = pipe
        let pipeFile = pipe.fileHandleForReading
        self.launch()
        
        let data = NSMutableData()
        while self.isRunning {
            data.append(pipeFile.availableData)
        }
        
        pipeFile.closeFile();
        self.terminate();
        
        if let output = String.init(data: data as Data, encoding: String.Encoding.utf8) {
            return AppSignerTaskOutput(status: self.terminationStatus, output: output)
        } else {
            return AppSignerTaskOutput(status: self.terminationStatus, output: "")
        }
        
    }
    
    /// 实时执行命令并通过回调返回输出
    func launchWithRealTimeOutput(outputCallback: @escaping (String) -> Void, completion: @escaping (Int32) -> Void) {
        self.standardInput = FileHandle.nullDevice
        let pipe = Pipe()
        self.standardOutput = pipe
        self.standardError = pipe
        let pipeFile = pipe.fileHandleForReading
        
        // 设置数据可用通知
        pipeFile.readabilityHandler = { fileHandle in
            let data = fileHandle.availableData
            if !data.isEmpty {
                if let output = String(data: data, encoding: .utf8) {
                    DispatchQueue.main.async {
                        outputCallback(output)
                    }
                }
            }
        }
        
        // 设置进程终止通知
        self.terminationHandler = { (process: Process) in
            // 确保读取所有剩余数据
            let remainingData = pipeFile.readDataToEndOfFile()
            if !remainingData.isEmpty {
                if let output = String(data: remainingData, encoding: .utf8) {
                    DispatchQueue.main.async {
                        outputCallback(output)
                    }
                }
            }
            
            pipeFile.readabilityHandler = nil
            DispatchQueue.main.async {
                completion(process.terminationStatus)
            }
        }
        
        // 在后台队列启动进程，避免阻塞UI
        DispatchQueue.global(qos: .userInitiated).async {
            self.launch()
        }
    }
    
    func execute(_ launchPath: String, workingDirectory: String?, arguments: [String]?)->AppSignerTaskOutput{
        self.launchPath = launchPath
        if arguments != nil {
            self.arguments = arguments
        }
        if workingDirectory != nil {
            self.currentDirectoryPath = workingDirectory!
        }
        return self.launchSynchronous()
    }
    
}
