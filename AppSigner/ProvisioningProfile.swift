//
//  provisioningProfile.swift
//  AppSigner
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/15.
//

import Foundation
import AppKit

enum ProfilePlatform {
    case iOS
    case macOS
}

// JSON 响应结构体
struct OnetoolsResponse: Codable {
    let total: Int
    let profiles: [OnetoolsProfile]
}

struct OnetoolsProfile: Codable {
    let name: String
    let fullIdentifier: String
    let type: String
    let typeCode: String
    let appId: String
    let teamId: String
    let created: String
    let expires: String
    let isExpired: Bool
    let filename: String
}

struct ProvisioningProfile {
    var filename: String,
        name: String,
        created: Date,
        expires: Date,
        appID: String,
        teamID: String,
        entitlements: [String : AnyObject]
        // application identifier，方便去重
        let fullIdentifier: String
    let type: String
    let typeCode: String
    let isExpired: Bool

        static func getProfiles(for platform: ProfilePlatform) -> [ProvisioningProfile] {
            // 使用 OnetoolsManager 执行命令
            guard let taskOutput = OnetoolsManager.shared.executeOnetools(
                arguments: ["ipa", "--profiles", "--json"],
                logHandler: { message in
                    NSLog(message)
                }
            ), taskOutput.status == 0 else {
                return []
            }
            
            // 解析 JSON 响应
            guard let jsonData = taskOutput.output.data(using: .utf8) else {
                NSLog("无法将输出转换为 Data")
                return []
            }
            
            do {
                let decoder = JSONDecoder()
                let response = try decoder.decode(OnetoolsResponse.self, from: jsonData)
                
                // 将 OnetoolsProfile 转换为 ProvisioningProfile
                let profiles = response.profiles.compactMap { onetoolsProfile -> ProvisioningProfile? in
                    return ProvisioningProfile(from: onetoolsProfile, platform: platform)
                }
                return profiles
            } catch {
                NSLog("JSON 解析失败: \(error)")
                return []
            }
        }
    
    // 从 OnetoolsProfile 创建 ProvisioningProfile
    init?(from onetoolsProfile: OnetoolsProfile, platform: ProfilePlatform) {
        // 解析日期
        let dateFormatter = ISO8601DateFormatter()
        guard let createdDate = dateFormatter.date(from: onetoolsProfile.created),
              let expiresDate = dateFormatter.date(from: onetoolsProfile.expires) else {
            NSLog("无法解析日期: created=\(onetoolsProfile.created), expires=\(onetoolsProfile.expires)")
            return nil
        }
        
        self.filename = onetoolsProfile.filename
        self.name = onetoolsProfile.name
        self.created = createdDate
        self.expires = expiresDate
        self.appID = onetoolsProfile.appId
        self.teamID = onetoolsProfile.teamId
        self.fullIdentifier = onetoolsProfile.fullIdentifier
        self.type = onetoolsProfile.type
        self.typeCode = onetoolsProfile.typeCode
        self.isExpired = onetoolsProfile.isExpired
        
        // 由于 onetools 没有提供 entitlements，我们设置为空字典
        // 如果需要 entitlements，可以单独解析 mobileprovision 文件
        self.entitlements = [:]
    }
}
