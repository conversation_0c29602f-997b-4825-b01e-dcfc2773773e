//
//  ProvisioningProfilePopUpButton.swift
//  AppSigner
//
//  Created by <PERSON><PERSON>ji<PERSON> on 2025/8/15.
//

import SwiftUI
import AppKit

struct ProvisioningProfilePopUpButton: NSViewRepresentable {
    @Binding var selectedProfile: String
    let profiles: [ProvisioningProfile]
    
    /// 根据选中的profile名称获取对应的完整文件路径
    /// - Parameter selectedProfileName: 选中的profile显示名称
    /// - Returns: 对应的文件路径，如果未找到或为""则返回nil
    func getSelectedProfilePath(_ selectedProfileName: String) -> String? {
        // 如果是""，返回nil
        if selectedProfileName == "" {
            return nil
        }
        
        // 查找匹配的profile
        for profile in profiles {
            let displayTitle = "\(profile.name)(\(profile.fullIdentifier))"
            if displayTitle == selectedProfileName {
                return profile.filename
            }
        }
        
        return nil
    }
    
    func makeNSView(context: Context) -> NSPopUpButton {
        let popUpButton = NSPopUpButton()
        popUpButton.target = context.coordinator
        popUpButton.action = #selector(Coordinator.selectionChanged(_:))
        
        // 设置内容压缩阻力和拥抱优先级，确保布局正确
        popUpButton.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        popUpButton.setContentHuggingPriority(.defaultLow, for: .horizontal)
        
        // 确保PopUpButton能够扩展以填充可用空间
        popUpButton.translatesAutoresizingMaskIntoConstraints = false
        
        return popUpButton
    }
    
    func updateNSView(_ nsView: NSPopUpButton, context: Context) {
        // 清空现有项目
        nsView.removeAllItems()
        
        // 添加"Re-Sign Only"作为第一项
        nsView.addItem(withTitle: "")
        
        // 添加所有Provisioning Profile项目，格式为：name(fullIdentifier)
        for profile in profiles {
            let displayTitle = "\(profile.name)(\(profile.fullIdentifier))"
            nsView.addItem(withTitle: displayTitle)
        }
        
        // 设置选中项
        let allTitles = [""] + profiles.map { "\($0.name)(\($0.fullIdentifier))" }
        if let index = allTitles.firstIndex(of: selectedProfile) {
            nsView.selectItem(at: index)
        } else {
            nsView.selectItem(at: 0) // 默认选择第一项
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject {
        let parent: ProvisioningProfilePopUpButton
        
        init(_ parent: ProvisioningProfilePopUpButton) {
            self.parent = parent
        }
        
        @objc func selectionChanged(_ sender: NSPopUpButton) {
            if let selectedTitle = sender.selectedItem?.title {
                parent.selectedProfile = selectedTitle
            }
        }
    }
}
