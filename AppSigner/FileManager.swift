//
//  FileManager.swift
//  AppSigner
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/15.
//

import SwiftUI
import AppKit

class FileManagerHelper: ObservableObject {
    
    /// 选择IPA文件
    /// - Parameter completion: 选择完成后的回调，返回选中的文件路径
    static func selectIPAFile(completion: @escaping (String?) -> Void) {
        let openPanel = NSOpenPanel()
        openPanel.title = "选择IPA文件"
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = false
        openPanel.canChooseFiles = true
        openPanel.allowedContentTypes = [.init(filenameExtension: "ipa")!]
        
        openPanel.begin { response in
            DispatchQueue.main.async {
                if response == .OK, let url = openPanel.url {
                    completion(url.path)
                } else {
                    completion(nil)
                }
            }
        }
    }
    
    /// 选择文件夹
    /// - Parameter completion: 选择完成后的回调，返回选中的文件夹路径
    static func selectDirectory(completion: @escaping (String?) -> Void) {
        let openPanel = NSOpenPanel()
        openPanel.title = "选择文件夹"
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = true
        openPanel.canChooseFiles = false
        
        openPanel.begin { response in
            DispatchQueue.main.async {
                if response == .OK, let url = openPanel.url {
                    completion(url.path)
                } else {
                    completion(nil)
                }
            }
        }
    }
}
