//
//  GetSignInfo.swift
//  AppSigner
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/15.
//
import Cocoa
import Foundation
let securityPath = "/usr/bin/security"

func getCodesigningCerts() -> [String] {
    var output: [String] = []
    let securityResult = Process().execute(securityPath, workingDirectory: nil, arguments: ["find-identity","-v","-p","codesigning"])
    if securityResult.output.count < 1 {
        return output
    }
    let rawResult = securityResult.output.components(separatedBy: "\"")
    
    for index in stride(from: 0, through: rawResult.count - 2, by: 2) {
        if !(rawResult.count - 1 < index + 1) {
            output.append(rawResult[index+1])
        }
    }
    return output.sorted()
}
