//
//  CertificatePopUpButton.swift
//  AppSigner
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/15.
//

import SwiftUI
import AppKit

struct CertificatePopUpButton: NSViewRepresentable {
    @Binding var selectedCertificate: String
    let certificates: [String]
    
    func makeNSView(context: Context) -> NSPopUpButton {
        let popUpButton = NSPopUpButton()
        popUpButton.target = context.coordinator
        popUpButton.action = #selector(Coordinator.selectionChanged(_:))
        
        // 设置内容压缩阻力和拥抱优先级，确保布局正确
        popUpButton.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        popUpButton.setContentHuggingPriority(.defaultLow, for: .horizontal)
        
        // 确保PopUpButton能够扩展以填充可用空间
        popUpButton.translatesAutoresizingMaskIntoConstraints = false
        
        return popUpButton
    }
    
    func updateNSView(_ nsView: NSPopUpButton, context: Context) {
        // 清空现有项目
        nsView.removeAllItems()
        
        // 获取证书列表并添加"Re-Sign Only"作为第一项
        var allCertificates = [""]
        allCertificates.append(contentsOf: certificates)
        
        // 添加所有项目
        for cert in allCertificates {
            nsView.addItem(withTitle: cert)
        }
        
        // 设置选中项
        if let index = allCertificates.firstIndex(of: selectedCertificate) {
            nsView.selectItem(at: index)
        } else {
            nsView.selectItem(at: 0) // 默认选择第一项
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject {
        let parent: CertificatePopUpButton
        
        init(_ parent: CertificatePopUpButton) {
            self.parent = parent
        }
        
        @objc func selectionChanged(_ sender: NSPopUpButton) {
            if let selectedTitle = sender.selectedItem?.title {
                parent.selectedCertificate = selectedTitle
            }
        }
    }
}
