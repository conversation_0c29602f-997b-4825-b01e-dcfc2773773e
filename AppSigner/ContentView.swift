//
//  ContentView.swift
//  AppSigner
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/13.
//

import SwiftUI
import AppKit

struct CheckboxToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            Image(systemName: configuration.isOn ? "checkmark.square" : "square")
                .foregroundColor(configuration.isOn ? .blue : .secondary)
                .onTapGesture {
                    configuration.isOn.toggle()
                }
            configuration.label
        }
    }
}

enum SigningShowType: String, CaseIterable {
    case resignOnly = "仅重签"
    case manual = "手动设置证书信息"
    case develop = "develop (自动根据BundleId获取dev开发证书)"
    case distribution = "distribution (自动根据BundleId获取dis发布证书)"
    case adhoc = "adhoc (自动根据BundleId获取adhoc测试证书)"
    
    var displayName: String {
        return self.rawValue
    }
}


struct ContentView: View {
    // 帮助信息文本
    private let helpMessage = """
Input File: 必选，要重签ipa包路径

Signing Type: 可选
• 仅重签: 使用当前ipa的签名信息重签
• 手动设置证书信息: 使用手动选择的证书和描述对其重签，Signing Certificate 和 Provisioning Profile必须设置。
• develop (自动根据BundleId获取dev开发证书)
• distribution (自动根据BundleId获取distribution开发证书)
• adhoc (自动根据BundleId获取adhoc开发证书)

New Bundle ID: 可选，新的bundleId，默认使用当前ipa的

App Display Name: 可选，应用显示名称，默认使用当前ipa的

App Short Version: 可选，App版本号，默认使用当前ipa的

App build Version: 可选，build版本号，默认使用当前ipa的

Add Files Path: 可选，要添加到.app目录的文件夹路径，使用方式：创建任意文件夹，按IPA解压后.app/根目录为基准，按层级添加目标文件，重签时将按层级进行拷贝

Delete Files Path: 可选，要删除的文件或文件夹路径，多个路径用逗号分隔，路径以.app根目录为基准

Dev Config Version: 可选，dev后台App-[打包编译配置]中的AppId和配置文件版本号，将请求到的配置文件替换到重签后的ipa中。格式：appid=1000000,env=develop
"""
    
    @State private var inputFile = ""
    @State private var signingCertificate = ""
    @State private var provisioningProfile = ""
    @State private var signingMethod = ""
    @State private var signingShowType: SigningShowType = .resignOnly
    @State private var newApplicationID = ""
    @State private var appDisplayName = ""
    @State private var appShortVersion = ""
    @State private var appBuildVersion = ""
    @State private var addFilesPath = ""
    @State private var deleteFilesPath = ""
    @State private var devEnvironmentConfig = ""
    @State private var logText = ""
    @State private var isReady = false
    @State private var keepTempDirectory = false
    @State private var availableCertificates: [String] = []
    @State private var availableProfiles: [ProvisioningProfile] = []
    @State private var isProcessing = false
    
    // 用于批量更新日志的缓冲区
    private let logUpdateQueue = DispatchQueue(label: "log.update.queue", qos: .userInitiated)
    @State private var logBuffer = ""
    @State private var logUpdateTimer: Timer?
    @State private var showingHelpAlert = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部表单区域 - 固定内容，距离顶部10
            VStack(spacing: 12) {
                // Input File
                HStack {
                    Text("Input File:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("ipa文件路径", text: $inputFile)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    Button("Browse") {
                        FileManagerHelper.selectIPAFile { selectedPath in
                            if let path = selectedPath {
                                inputFile = path
                            }
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                // Signing Type
                HStack {
                    Text("Signing Type:")
                        .frame(width: 120, alignment: .trailing)
                    Picker("Signing Type", selection: $signingShowType) {
                        ForEach(SigningShowType.allCases, id: \.self) { type in
                            Text(type.displayName).tag(type)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .onChange(of: signingShowType) { newValue in
                        updateSigningSettings(for: newValue)
                    }
                }
                
                // Signing Certificate - 仅在"手动设置证书信息"时显示
                // Provisioning Profile - 仅在"手动设置证书信息"时显示
                if signingShowType == .manual {
                    HStack {
                        Text("Signing Certificate:")
                            .frame(width: 120, alignment: .trailing)
                        CertificatePopUpButton(
                            selectedCertificate: $signingCertificate,
                            certificates: availableCertificates
                        )
                        .frame(maxWidth: .infinity)
                    }
                    
                    HStack {
                        Text("Provisioning Profile:")
                            .frame(width: 120, alignment: .trailing)
                        ProvisioningProfilePopUpButton(
                            selectedProfile: $provisioningProfile,
                            profiles: availableProfiles
                        )
                        .frame(maxWidth: .infinity)
                    }
                }
                
                // New Application ID
                HStack {
                    Text("New Bundle ID:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("新的Bundle ID", text: $newApplicationID)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // App Display Name
                HStack {
                    Text("App Display Name:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("应用显示名称", text: $appDisplayName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // App Version
                HStack {
                    Text("App Short Version:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("应用版本号", text: $appShortVersion)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // App Short Version
                HStack {
                    Text("App build Version:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("This changes the app short version number", text: $appBuildVersion)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // Add Files Path
                HStack {
                    Text("Add Files Path:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("要添加到.app目录的文件夹路径，该文件夹内的文件结构对应IPA解压后.app目录的根目录结构", text: $addFilesPath)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    Button("Browse") {
                        FileManagerHelper.selectDirectory { selectedPath in
                            if let path = selectedPath {
                                addFilesPath = path
                            }
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                // Delete Files Path
                HStack {
                    Text("Delete Files Path:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("要删除的文件或文件夹路径，多个路径用逗号分隔，路径以.app根目录为基准", text: $deleteFilesPath)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // Dev Environment Config
                HStack {
                    Text("Dev Config Version:")
                        .frame(width: 120, alignment: .trailing)
                    TextField("dev后台App对应的AppId和配置文件版本号，将请求到的配置文件替换到重签后的ipa中。格式：appid=1000000,env=develop", text: $devEnvironmentConfig)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // Status and Start button
                HStack {
                    Text(isReady ? "Ready" : "")
                        .foregroundColor(.secondary)
                        .padding(.leading, 20)
                    HStack {
                        Toggle("保留临时目录", isOn: $keepTempDirectory)
                            .toggleStyle(CheckboxToggleStyle())
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        Button(action: {
                            showingHelpAlert = true
                        }) {
                            Image(systemName: "questionmark.circle")
                        }
                        .buttonStyle(.bordered)
                        .help("显示帮助信息")
                        
                        Button("Start") {
                            startResigning()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(isProcessing || inputFile.isEmpty)
                    }
                    .padding(.trailing, 20)
                }
            }
            .padding(.top, 10)
            .padding(.horizontal, 20)
            
            // 中间的弹性空间
            Spacer(minLength: 20)
            
            // 底部日志区域 - 距离底部10
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("日志:")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button("清空日志") {
                        clearLog()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
                
                // 日志显示区域 - 使用可变高度
                ScrollViewReader { proxy in
                    ScrollView {
                        VStack(alignment: .leading, spacing: 0) {
                            Text(logText)
                                .font(.system(.body, design: .monospaced))
                                .textSelection(.enabled)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(8)
                                .id("logContent")
                        }
                    }
                    .frame(minHeight: 150, maxHeight: .infinity)
                    .background(Color(NSColor.textBackgroundColor))
                    .border(Color.secondary.opacity(0.3), width: 1)
                    .onChange(of: logText) { _ in
                        // 自动滚动到底部
                        withAnimation(.easeOut(duration: 0.1)) {
                            proxy.scrollTo("logContent", anchor: .bottom)
                        }
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 10)
        }
        .frame(minWidth: 600, minHeight: 600)
        .onAppear {
            // 视图出现时加载证书列表和Provisioning Profile列表
            availableCertificates = getCodesigningCerts()
            availableProfiles = ProvisioningProfile.getProfiles(for: .iOS)
            // 初始化签名设置
            updateSigningSettings(for: signingShowType)
        }
        .alert("使用帮助", isPresented: $showingHelpAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(helpMessage)
        }
    }
    
    // MARK: - Private Methods
    
    private func updateSigningSettings(for SigningShowType: SigningShowType) {
        switch SigningShowType {
        case .resignOnly:
            signingCertificate = ""
            provisioningProfile = ""
            signingMethod = ""
        case .manual:
            signingMethod = ""
            // 保持当前的signingCertificate和provisioningProfile值
        case .develop:
            signingCertificate = ""
            provisioningProfile = ""
            signingMethod = "dev"
        case .distribution:
            signingCertificate = ""
            provisioningProfile = ""
            signingMethod = "dis"
        case .adhoc:
            signingCertificate = ""
            provisioningProfile = ""
            signingMethod = "adhoc"
        }
    }
    
    private func startResigning() {
        // 1. 判断inputFile是否为空
        guard !inputFile.isEmpty else {
            appendToLog("错误: 请选择要重签名的IPA文件")
            return
        }
        
        // 2. 如果选择了"手动设置证书信息"，检查证书和配置文件是否已选择
        if signingShowType == .manual {
            if signingCertificate.isEmpty {
                appendToLog("错误: 请选择签名证书")
                return
            }
            if provisioningProfile.isEmpty {
                appendToLog("错误: 请选择配置文件")
                return
            }
        }
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: inputFile) else {
            appendToLog("错误: 选择的IPA文件不存在: \(inputFile)")
            return
        }
        
        isProcessing = true
        logText = "" // 清空日志
        appendToLog("开始重签名处理...")
        
        // 验证并准备 onetools 工具
        guard let onetoolsPath = OnetoolsManager.shared.validateAndPrepareOnetools(logHandler: appendToLog) else {
            isProcessing = false
            return
        }
        
        // 构建onetools命令参数
        let arguments = buildOnetoolsArguments()
        appendToLog("执行命令: \(onetoolsPath) \(arguments.joined(separator: " "))")
        
        // 执行命令
        let process = Process()
        process.launchPath = onetoolsPath
        process.arguments = arguments
        
        // 启动日志更新定时器
        startLogUpdateTimer()
        
        process.launchWithRealTimeOutput(
            outputCallback: { output in
                self.appendToLogBuffer(output)
            },
            completion: { exitCode in
                // 停止定时器并刷新最后的日志
                self.stopLogUpdateTimer()
                self.flushLogBuffer()
                
                self.isProcessing = false
                if exitCode == 0 {
                    self.appendToLog("重签名完成!")
                } else {
                    self.appendToLog("重签名失败，退出码: \(exitCode)")
                }
            }
        )
    }
    
    private func buildOnetoolsArguments() -> [String] {
        var arguments = ["ipa", "resign"]
        
        // 必传参数：ipa文件路径
        arguments.append(contentsOf: ["-p", inputFile])
        
        // 2. signingCertificate为""时不用设置
        if signingCertificate != "" && !signingCertificate.isEmpty {
            arguments.append(contentsOf: ["--cert", signingCertificate])
        }
        
        // 3. provisioningProfile为""时不用设置
        if provisioningProfile != "" {
            if let profilePath = getSelectedProvisioningProfilePath() {
                arguments.append(contentsOf: ["--provision", profilePath])
            }
        }
        
        // 4. signingMethod为""时不用设置
        if signingMethod != "" {
            arguments.append(contentsOf: ["--cert-type", signingMethod])
        }
        if !newApplicationID.isEmpty {
            arguments.append(contentsOf: ["--bundle-id", newApplicationID])
        }
        
        if !appDisplayName.isEmpty {
            arguments.append(contentsOf: ["--display-name", appDisplayName])
        }
        
        if !appShortVersion.isEmpty {
            arguments.append(contentsOf: ["--app-version", appShortVersion])
        }
        
        if !appBuildVersion.isEmpty {
            arguments.append(contentsOf: ["--build-version", appBuildVersion])
        }
        
        if !addFilesPath.isEmpty {
            arguments.append(contentsOf: ["--add-files-path", addFilesPath])
        }
        
        if !deleteFilesPath.isEmpty {
            arguments.append(contentsOf: ["--delete-files", deleteFilesPath])
        }
        
        if !devEnvironmentConfig.isEmpty {
            arguments.append(contentsOf: ["--app-dev-config", devEnvironmentConfig])
        }
        
        // 5. 保留临时目录对应--keep-temp-dir参数
        if keepTempDirectory {
            arguments.append("--keep-temp-dir")
        }
        
        // 自动生成输出文件路径
        let outputPath = generateOutputPath()
        arguments.append(contentsOf: ["-o", outputPath])
        
        return arguments
    }
    
    private func getSelectedProvisioningProfilePath() -> String? {
        // 如果是""，返回nil
        if provisioningProfile == "" {
            return nil
        }
        
        // 查找匹配的profile
        for profile in availableProfiles {
            let displayTitle = "\(profile.name)(\(profile.fullIdentifier))"
            if displayTitle == provisioningProfile {
                return profile.filename
            }
        }
        
        return nil
    }
    

    
    private func generateOutputPath() -> String {
        let inputURL = URL(fileURLWithPath: inputFile)
        let directory = inputURL.deletingLastPathComponent()
        let filename = inputURL.deletingPathExtension().lastPathComponent
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = formatter.string(from: Date())
        
        let outputFilename = "\(filename)_resigned_\(timestamp).ipa"
        return directory.appendingPathComponent(outputFilename).path
    }
    
    private func appendToLog(_ text: String) {
        DispatchQueue.main.async {
            if !self.logText.isEmpty {
                self.logText += "\n"
            }
            self.logText += text
        }
    }
    
    // MARK: - Log Buffer Management
    
    private func appendToLogBuffer(_ text: String) {
        logUpdateQueue.async {
            self.logBuffer += text
        }
    }
    
    private func startLogUpdateTimer() {
        DispatchQueue.main.async {
            self.logUpdateTimer = Timer.scheduledTimer(
                withTimeInterval: 0.1, repeats: true) { _ in
                self.flushLogBuffer()
            }
        }
    }
    
    private func stopLogUpdateTimer() {
        DispatchQueue.main.async {
            self.logUpdateTimer?.invalidate()
            self.logUpdateTimer = nil
        }
    }
    
    private func flushLogBuffer() {
        logUpdateQueue.async {
            let bufferedText = self.logBuffer
            if !bufferedText.isEmpty {
                self.logBuffer = ""
                DispatchQueue.main.async {
                    self.logText += bufferedText
                }
            }
        }
    }
    
    private func clearLog() {
        logUpdateQueue.async {
            self.logBuffer = ""
            DispatchQueue.main.async {
                self.logText = ""
            }
        }
    }
}

#Preview {
    ContentView()
}
