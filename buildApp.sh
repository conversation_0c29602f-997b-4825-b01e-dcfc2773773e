#!/bin/sh
projectPath=$(pwd)
rm -rf ./build

app_zip_name="AppSigner.zip"
if [ $# -gt 0 ]; then
  app_zip_name=$1
fi



xcodebuild clean -configuration Release -scheme AppSigner

xcodebuild archive -configuration Release -scheme AppSigner -target AppSigner -archivePath "${projectPath}/build/AppSigner.xcarchive"  ARCHS="arm64" ONLY_ACTIVE_ARCH=NO

# xcodebuild -exportArchive -archivePath "${projectPath}/build/AppSigner.xcarchive" -configuration Release -exportPath "${projectPath}/build/dev" -exportOptionsPlist exportOptionsPlist_dev.plist
cp -rf ${projectPath}/build/AppSigner.xcarchive/Products/Applications/AppSigner.app ${projectPath}/build/AppSigner.app

cd ./build
zip -q -r -o ${app_zip_name} AppSigner.app

